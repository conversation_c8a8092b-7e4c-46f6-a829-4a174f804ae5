/**
 * Chatbot Button Component for MiamBidi
 * Floating action button to open/close the chatbot
 */

import React from 'react';
import {
  Fab,
  Badge,
  Zoom,
  Tooltip,
  Box
} from '@mui/material';
import {
  <PERSON><PERSON>,
  <PERSON>,
  SmartToy
} from '@mui/icons-material';

function ChatbotButton({
  isOpen,
  onClick,
  hasNewMessage = false,
  isTyping = false,
  disabled = false
}) {
  return (
    <Box
      sx={{
        position: 'fixed',
        bottom: 24,
        right: 24,
        zIndex: 1300, // Above most MUI components
        '& .MuiFab-root': {
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            transform: 'scale(1.1)',
            boxShadow: '0 8px 25px rgba(46, 125, 50, 0.3)'
          }
        }
      }}
    >
      <Zoom in={true} timeout={300}>
        <Tooltip 
          title={isOpen ? "Fermer l'assistant" : "Assistant Culinaire MiamBidi"}
          placement="left"
          arrow
        >
          <Badge
            badgeContent={hasNewMessage ? "!" : 0}
            color="error"
            overlap="circular"
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
          >
            <Fab
              color="primary"
              size="large"
              onClick={onClick}
              disabled={disabled}
              sx={{
                width: 60,
                height: 60,
                bgcolor: '#2E7D32', // MiamBidi green
                color: 'white',
                '&:hover': {
                  bgcolor: '#1B5E20'
                },
                '&:disabled': {
                  bgcolor: 'grey.400',
                  color: 'grey.600'
                },
                // Pulsing animation when typing
                ...(isTyping && {
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%': {
                      boxShadow: '0 0 0 0 rgba(46, 125, 50, 0.7)'
                    },
                    '70%': {
                      boxShadow: '0 0 0 10px rgba(46, 125, 50, 0)'
                    },
                    '100%': {
                      boxShadow: '0 0 0 0 rgba(46, 125, 50, 0)'
                    }
                  }
                })
              }}
            >
              {isOpen ? (
                <Close sx={{ fontSize: 28 }} />
              ) : isTyping ? (
                <SmartToy sx={{ fontSize: 28 }} />
              ) : (
                <Chat sx={{ fontSize: 28 }} />
              )}
            </Fab>
          </Badge>
        </Tooltip>
      </Zoom>
    </Box>
  );
}

export default ChatbotButton;
