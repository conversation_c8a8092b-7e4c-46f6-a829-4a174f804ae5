/**
 * <PERSON>tbot Panel Component for MiamBidi
 * Main chat interface with slide-out panel design
 */

import React, { useState } from 'react';
import {
  Paper,
  Box,
  Typography,
  TextField,
  IconButton,
  Avatar,
  Slide,
  Divider,
  Button,
  Chip,
  CircularProgress,
  <PERSON>ert,
  Tooltip
} from '@mui/material';
import {
  Close,
  Minimize,
  Send,
  SmartToy,
  Person,
  Clear,
  QuestionAnswer
} from '@mui/icons-material';

function ChatbotPanel({
  isOpen,
  onClose,
  messages,
  inputValue,
  onInputChange,
  onSubmit,
  isTyping,
  isLoading,
  error,
  messagesEndRef,
  chatContainerRef,
  quickActions,
  onClearConversation
}) {
  const [isMinimized, setIsMinimized] = useState(false);

  const handleQuickAction = (action) => {
    onInputChange(action);
    // Auto-submit quick actions
    const fakeEvent = { preventDefault: () => {} };
    onSubmit(fakeEvent);
  };

  const formatMessageContent = (content) => {
    // Simple formatting for line breaks
    return content.split('\n').map((line, index) => (
      <React.Fragment key={index}>
        {line}
        {index < content.split('\n').length - 1 && <br />}
      </React.Fragment>
    ));
  };

  const getMessageTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Slide direction="left" in={isOpen} mountOnEnter unmountOnExit>
      <Paper
        elevation={8}
        sx={{
          position: 'fixed',
          top: 0,
          right: 0,
          width: { xs: '100vw', sm: '400px' },
          height: '100vh',
          zIndex: 1400,
          display: 'flex',
          flexDirection: 'column',
          bgcolor: 'background.paper',
          borderRadius: { xs: 0, sm: '16px 0 0 16px' },
          overflow: 'hidden',
          transition: 'all 0.3s ease-out',
          ...(isMinimized && {
            height: '60px',
            overflow: 'hidden'
          })
        }}
      >
        {/* Header */}
        <Box
          sx={{
            p: 2,
            bgcolor: '#2E7D32',
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            minHeight: '60px'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar sx={{ bgcolor: '#1B5E20', width: 32, height: 32 }}>
              <SmartToy fontSize="small" />
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 600 }}>
                Assistant Culinaire MiamBidi
              </Typography>
              <Typography variant="caption" sx={{ opacity: 0.9 }}>
                {isTyping ? 'En train d\'écrire...' : 'En ligne'}
              </Typography>
            </Box>
          </Box>
          
          <Box>
            <Tooltip title={isMinimized ? "Agrandir" : "Réduire"}>
              <IconButton
                size="small"
                onClick={() => setIsMinimized(!isMinimized)}
                sx={{ color: 'white', mr: 1 }}
              >
                <Minimize />
              </IconButton>
            </Tooltip>
            <Tooltip title="Fermer">
              <IconButton
                size="small"
                onClick={onClose}
                sx={{ color: 'white' }}
              >
                <Close />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {!isMinimized && (
          <>
            {/* Error Alert */}
            {error && (
              <Alert severity="warning" sx={{ m: 1 }}>
                {error}
              </Alert>
            )}

            {/* Messages Container */}
            <Box
              ref={chatContainerRef}
              sx={{
                flexGrow: 1,
                overflow: 'auto',
                p: 1,
                display: 'flex',
                flexDirection: 'column',
                gap: 1,
                bgcolor: '#f5f5f5'
              }}
            >
              {messages.map((message, index) => (
                <Box
                  key={message.id || index}
                  sx={{
                    display: 'flex',
                    justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
                    mb: 1
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: 1,
                      maxWidth: '85%',
                      flexDirection: message.role === 'user' ? 'row-reverse' : 'row'
                    }}
                  >
                    <Avatar
                      sx={{
                        width: 28,
                        height: 28,
                        bgcolor: message.role === 'user' ? '#1976d2' : '#2E7D32',
                        fontSize: '0.8rem'
                      }}
                    >
                      {message.role === 'user' ? <Person fontSize="small" /> : <SmartToy fontSize="small" />}
                    </Avatar>
                    
                    <Paper
                      elevation={1}
                      sx={{
                        p: 1.5,
                        bgcolor: message.role === 'user' ? '#e3f2fd' : 'white',
                        borderRadius: message.role === 'user' ? '16px 16px 4px 16px' : '16px 16px 16px 4px',
                        border: message.isError ? '1px solid #ff9800' : 'none',
                        animation: 'fadeIn 0.3s ease-in',
                        '@keyframes fadeIn': {
                          from: { opacity: 0, transform: 'translateY(10px)' },
                          to: { opacity: 1, transform: 'translateY(0)' }
                        }
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          color: message.role === 'user' ? '#1565c0' : '#333',
                          lineHeight: 1.4,
                          whiteSpace: 'pre-wrap'
                        }}
                      >
                        {formatMessageContent(message.content)}
                      </Typography>
                      <Typography
                        variant="caption"
                        sx={{
                          color: 'text.secondary',
                          display: 'block',
                          mt: 0.5,
                          textAlign: message.role === 'user' ? 'right' : 'left'
                        }}
                      >
                        {getMessageTime(message.timestamp)}
                      </Typography>
                    </Paper>
                  </Box>
                </Box>
              ))}

              {/* Typing Indicator */}
              {isTyping && (
                <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    <Avatar sx={{ width: 28, height: 28, bgcolor: '#2E7D32' }}>
                      <SmartToy fontSize="small" />
                    </Avatar>
                    <Paper
                      elevation={1}
                      sx={{
                        p: 1.5,
                        bgcolor: 'white',
                        borderRadius: '16px 16px 16px 4px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1
                      }}
                    >
                      <CircularProgress size={16} sx={{ color: '#2E7D32' }} />
                      <Typography variant="body2" color="text.secondary">
                        Réflexion en cours...
                      </Typography>
                    </Paper>
                  </Box>
                </Box>
              )}

              <div ref={messagesEndRef} />
            </Box>

            {/* Quick Actions */}
            {quickActions && quickActions.length > 0 && (
              <Box sx={{ p: 1, borderTop: '1px solid #e0e0e0' }}>
                <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                  Suggestions rapides:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {quickActions.slice(0, 3).map((action, index) => (
                    <Chip
                      key={index}
                      label={action}
                      size="small"
                      onClick={() => handleQuickAction(action)}
                      sx={{
                        fontSize: '0.7rem',
                        height: '24px',
                        '&:hover': { bgcolor: '#e8f5e8' }
                      }}
                    />
                  ))}
                </Box>
              </Box>
            )}

            {/* Input Area */}
            <Box sx={{ p: 2, borderTop: '1px solid #e0e0e0', bgcolor: 'white' }}>
              <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                <Button
                  size="small"
                  startIcon={<Clear />}
                  onClick={onClearConversation}
                  sx={{ fontSize: '0.7rem' }}
                >
                  Effacer
                </Button>
                <Button
                  size="small"
                  startIcon={<QuestionAnswer />}
                  onClick={() => handleQuickAction("Comment utiliser l'application ?")}
                  sx={{ fontSize: '0.7rem' }}
                >
                  Aide
                </Button>
              </Box>
              
              <form onSubmit={onSubmit}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <TextField
                    fullWidth
                    size="small"
                    placeholder="Tapez votre message..."
                    value={inputValue}
                    onChange={(e) => onInputChange(e.target.value)}
                    disabled={isLoading}
                    multiline
                    maxRows={3}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '20px'
                      }
                    }}
                  />
                  <IconButton
                    type="submit"
                    disabled={!inputValue.trim() || isLoading}
                    sx={{
                      bgcolor: '#2E7D32',
                      color: 'white',
                      '&:hover': { bgcolor: '#1B5E20' },
                      '&:disabled': { bgcolor: 'grey.300' }
                    }}
                  >
                    {isLoading ? <CircularProgress size={20} /> : <Send />}
                  </IconButton>
                </Box>
              </form>
            </Box>
          </>
        )}
      </Paper>
    </Slide>
  );
}

export default ChatbotPanel;
